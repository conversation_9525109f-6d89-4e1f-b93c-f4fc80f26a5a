import 'package:isar/isar.dart';

import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../../services/database/isar_service.dart';

// Legacy alias for compatibility
typedef TransactionsHandler = TransactionsRepository;

/// Pure reactive repository for Transactions module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class TransactionsRepository {
  final IsarService _isarService;

  // Constructor with explicit dependency injection
  TransactionsRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE TRANSACTIONS STREAMS ===//

  /// Watches all transactions with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<TransactionIsar>> watchAllTransactions() {
    return _isar.transactionIsars.where().watch(fireImmediately: true);
  }

  /// Watches all categories with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<CategoryIsar>> watchAllCategories() {
    return _isar.categoryIsars.where().watch(fireImmediately: true);
  }

  //=== TRANSACTIONS CRUD ===//

  /// Save (add or update) a transaction using Isar's native upsert
  Future<void> saveTransaction(TransactionIsar transaction) async {
    await _isar.writeTxn(() async {
      await _isar.transactionIsars.put(transaction);
    });
  }

  /// Delete a transaction by its Isar ID
  Future<void> deleteTransaction(int id) async {
    await _isar.writeTxn(() async {
      await _isar.transactionIsars.delete(id);
    });
  }

  //=== CATEGORIES CRUD ===//

  /// Save (add or update) a category using Isar's native upsert
  Future<void> saveCategory(CategoryIsar category) async {
    await _isar.writeTxn(() async {
      await _isar.categoryIsars.put(category);
    });
  }

  /// Delete a category by its Isar ID
  Future<void> deleteCategory(int id) async {
    await _isar.writeTxn(() async {
      await _isar.categoryIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all transactions (for reports and analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<TransactionIsar>> getAllTransactions() async {
    return await _isar.transactionIsars.where().findAll();
  }
}
