# Implementation Plan

## Phase 0: Foundation Setup (Week 0)

- [ ] 0. Establish Foundation Templates and Infrastructure
  - Create standardized base classes and templates for consistent implementation
  - Establish architectural compliance patterns
  - _Requirements: 7.1, 7.2, 7.3_

## Phase 1: High Priority Module Refactoring (Weeks 1-3)

- [ ] 1. Refactor Reports Module to Follow Established Patterns
  - Create pure reactive repository with Stream-only operations
  - Implement dual-stream controller with filtered/unfiltered data separation
  - Extract analytics logic to dedicated service
  - Remove direct database access patterns
  - _Requirements: 1.1, 1.2, 2.3, 2.4, 2.5_

- [ ] 1.1 Create Reports Repository Following Cattle/Weight Pattern
  - Write `ReportsRepository` class with pure reactive streams
  - Implement `watchAllReports()` method with `fireImmediately: true`
  - Add CRUD methods: `saveReport()`, `deleteReport()`, `getAllReports()`
  - Remove all business logic and error handling from repository layer
  - Use explicit dependency injection through constructor
  - _Requirements: 1.1, 2.4, 5.4_

```dart
// BEFORE: reports_service.dart (violates pattern)
class ReportsService {
  // ❌ Direct database access
  Future<ReportData> getDashboardReport(FilterState filter) async {
    final cattle = await GetIt.instance<Isar>().cattleIsars.where().findAll();
    // ❌ Business logic mixed with data access
    final metrics = <String, ReportMetric>{};
    // Complex business logic here...
  }
}

// AFTER: reports_repository.dart (follows pure pattern)
class ReportsRepository {
  final IsarService _isarService;
  
  // ✅ Explicit dependency injection
  ReportsRepository(this._isarService);
  
  Isar get _isar => _isarService.isar;
  
  // ✅ Stream-only operations
  Stream<List<ReportIsar>> watchAllReports() {
    return _isar.reportIsars.where().watch(fireImmediately: true);
  }
  
  Stream<List<ReportIsar>> watchReportsByType(String type) {
    return _isar.reportIsars.filter().typeEqualTo(type).watch(fireImmediately: true);
  }
  
  // ✅ Simple CRUD operations - exceptions bubble up naturally
  Future<void> saveReport(ReportIsar report) async {
    await _isar.writeTxn(() async {
      await _isar.reportIsars.put(report);
    });
  }
  
  Future<void> deleteReport(int reportId) async {
    await _isar.writeTxn(() async {
      await _isar.reportIsars.delete(reportId);
    });
  }
  
  // ✅ Query methods for analytics (Future<List> for one-time fetching)
  Future<List<ReportIsar>> getAllReports() async {
    return await _isar.reportIsars.where().findAll();
  }
  
  Future<List<ReportIsar>> getReportsByDateRange(DateTime start, DateTime end) async {
    return await _isar.reportIsars
        .filter()
        .createdAtBetween(start, end)
        .findAll();
  }
}
```

- [ ] 1.2 Create Reports Controller with Dual-Stream Architecture
  - Implement `ReportsController` extending `ChangeNotifier`
  - Add separate `_unfilteredStreamSubscription` and `_filteredStreamSubscription`
  - Create `_unfilteredReports` and `_filteredReports` data separation
  - Implement `applyFilters()` method with database-level filtering
  - Add analytics calculation using unfiltered data only
  - _Requirements: 1.2, 2.3, 5.5_

```dart
// AFTER: reports_controller.dart (follows dual-stream pattern)
class ReportsController extends ChangeNotifier {
  // Repositories with lazy getters to avoid constructor GetIt access
  ReportsRepository get _reportsRepository => GetIt.instance<ReportsRepository>();
  CattleRepository get _cattleRepository => GetIt.instance<CattleRepository>();
  Isar get _isar => GetIt.instance<Isar>();
  
  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  
  // Stream subscriptions - Separated for filtered/unfiltered data
  StreamSubscription<List<ReportIsar>>? _unfilteredStreamSubscription;
  StreamSubscription<List<ReportIsar>>? _filteredStreamSubscription;
  
  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<ReportIsar> _unfilteredReports = []; // Complete dataset for analytics calculations
  List<ReportIsar> _filteredReports = [];   // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied
  
  // Analytics result - ALWAYS calculated on unfiltered data
  ReportsAnalyticsResult _analyticsResult = ReportsAnalyticsResult.empty;
  
  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  
  /// Returns the filtered reports for UI display
  List<ReportIsar> get reports => List.unmodifiable(_filteredReports);
  
  /// Returns the complete unfiltered reports for analytics
  List<ReportIsar> get unfilteredReports => List.unmodifiable(_unfilteredReports);
  
  // Main analytics object - single source of truth
  ReportsAnalyticsResult get analytics => _analyticsResult;
  
  // Constructor
  ReportsController() {
    _initializeStreamListener();
  }
  
  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListener() {
    // Primary stream: Unfiltered data for analytics calculations
    _unfilteredStreamSubscription = _isar.reportIsars.where()
        .watch(fireImmediately: true)
        .listen((unfilteredList) {
      _handleUnfilteredDataUpdate(unfilteredList);
    });
    
    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredReports = _unfilteredReports;
    _hasActiveFilters = false;
  }
  
  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(List<ReportIsar> unfilteredList) async {
    try {
      // Update the complete unfiltered dataset
      _unfilteredReports = unfilteredList;
      
      // ALWAYS calculate analytics on the complete unfiltered dataset
      if (_unfilteredReports.isEmpty) {
        _analyticsResult = ReportsAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }
      
      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredReports = List.from(_unfilteredReports);
      }
      
      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update report data: $e';
      notifyListeners();
    }
  }
  
  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = ReportsAnalyticsService.calculate(
      _unfilteredReports, // Use unfiltered data for accurate analytics
    );
  }
  
  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
```

- [ ] 1.3 Extract Reports Analytics to Dedicated Service
  - Create `ReportsAnalyticsService` as pure calculation functions
  - Implement single-pass O(n) efficiency algorithms
  - Move all analytics logic from `ReportsService` to analytics service
  - Create `ReportsAnalyticsResult` data class for immutable results
  - Remove state from analytics service (pure functions only)
  - _Requirements: 2.5, 5.4_

```dart
// NEW FILE: reports_analytics_service.dart
/// Data class to hold all analytics results
class ReportsAnalyticsResult {
  final int totalReports;
  final int generatedThisMonth;
  final int generatedThisWeek;
  final Map<String, int> reportsByType;
  final Map<String, int> reportsByStatus;
  final double averageGenerationTime;
  final int mostActiveReportType;
  
  const ReportsAnalyticsResult({
    required this.totalReports,
    required this.generatedThisMonth,
    required this.generatedThisWeek,
    required this.reportsByType,
    required this.reportsByStatus,
    required this.averageGenerationTime,
    required this.mostActiveReportType,
  });
  
  /// Empty result for when there's no data
  static const empty = ReportsAnalyticsResult(
    totalReports: 0,
    generatedThisMonth: 0,
    generatedThisWeek: 0,
    reportsByType: {},
    reportsByStatus: {},
    averageGenerationTime: 0.0,
    mostActiveReportType: 0,
  );
}

/// Pure analytics service - no state, just calculations
class ReportsAnalyticsService {
  /// Main calculation method - single entry point with O(n) efficiency
  static ReportsAnalyticsResult calculate(List<ReportIsar> reports) {
    if (reports.isEmpty) {
      return ReportsAnalyticsResult.empty;
    }
    
    // Single pass through the data for maximum efficiency - O(n) instead of O(3n)
    final accumulator = _ReportsAnalyticsAccumulator();
    
    for (final report in reports) {
      accumulator.process(report);
    }
    
    return accumulator.toResult();
  }
}

/// Efficient single-pass accumulator for all analytics calculations
class _ReportsAnalyticsAccumulator {
  // Basic counts
  int totalReports = 0;
  int generatedThisMonth = 0;
  int generatedThisWeek = 0;
  
  // Distribution maps
  final Map<String, int> reportsByType = {};
  final Map<String, int> reportsByStatus = {};
  
  // Time metrics
  double totalGenerationTime = 0.0;
  int reportsWithGenerationTime = 0;
  
  /// Process a single report record - updates all metrics in one pass
  void process(ReportIsar report) {
    totalReports++;
    
    // Date-based metrics
    final now = DateTime.now();
    if (report.createdAt != null) {
      if (report.createdAt!.month == now.month && report.createdAt!.year == now.year) {
        generatedThisMonth++;
      }
      
      final weekAgo = now.subtract(const Duration(days: 7));
      if (report.createdAt!.isAfter(weekAgo)) {
        generatedThisWeek++;
      }
    }
    
    // Type distribution
    final type = report.type ?? 'Unknown';
    reportsByType[type] = (reportsByType[type] ?? 0) + 1;
    
    // Status distribution
    final status = report.status ?? 'Unknown';
    reportsByStatus[status] = (reportsByStatus[status] ?? 0) + 1;
    
    // Generation time metrics
    if (report.generationTime != null && report.generationTime! > 0) {
      totalGenerationTime += report.generationTime!;
      reportsWithGenerationTime++;
    }
  }
  
  /// Convert accumulated data to immutable result
  ReportsAnalyticsResult toResult() {
    final averageGenerationTime = reportsWithGenerationTime > 0
        ? totalGenerationTime / reportsWithGenerationTime
        : 0.0;
    
    final mostActiveReportType = reportsByType.values.isNotEmpty
        ? reportsByType.values.reduce((a, b) => a > b ? a : b)
        : 0;
    
    return ReportsAnalyticsResult(
      totalReports: totalReports,
      generatedThisMonth: generatedThisMonth,
      generatedThisWeek: generatedThisWeek,
      reportsByType: Map.unmodifiable(reportsByType),
      reportsByStatus: Map.unmodifiable(reportsByStatus),
      averageGenerationTime: averageGenerationTime,
      mostActiveReportType: mostActiveReportType,
    );
  }
}
```

- [ ] 1.4 Remove Direct Database Access from Reports Service
  - Replace direct Isar queries with repository method calls
  - Remove `GetIt.instance<Isar>()` usage from service layer
  - Update all report generation methods to use repository
  - Ensure service layer only handles business logic, not data access
  - _Requirements: 1.2, 2.3_

- [ ] 2. Refactor Transactions Module to Standard Architecture
  - Implement proper reactive repository pattern
  - Create dual-stream controller with analytics separation
  - Add dedicated analytics and sync services
  - Remove mixed concerns from transaction service
  - _Requirements: 1.1, 1.2, 2.3, 2.4, 2.5_

- [ ] 2.1 Create Transactions Repository with Reactive Streams
  - Write `TransactionsRepository` following cattle module pattern
  - Implement `watchAllTransactions()` with reactive updates
  - Add proper CRUD methods with Isar's native upsert
  - Remove business logic from existing repository methods
  - Add query methods for analytics: `getAllTransactions()`, `getTransactionsByDateRange()`
  - _Requirements: 1.1, 2.4, 5.4_

- [ ] 2.2 Implement Transactions Controller with Stream Management
  - Create `TransactionsController` with dual-stream pattern
  - Separate unfiltered data for analytics and filtered data for UI
  - Implement proper filter application at database level
  - Add state management with `ControllerState` enum
  - Include analytics calculation on unfiltered data only
  - _Requirements: 1.2, 2.3, 5.5_

- [ ] 2.3 Create Dedicated Transaction Analytics Service
  - Extract analytics logic from `TransactionService`
  - Implement `TransactionAnalyticsService.calculate()` as pure function
  - Create single-pass accumulator for O(n) efficiency
  - Add financial metrics calculation (income, expenses, profit)
  - Remove state and make service purely functional
  - _Requirements: 2.5, 5.4_

- [ ] 2.4 Add Transaction Sync Service
  - Create `TransactionSyncService` following cattle sync pattern
  - Implement bidirectional sync with external API
  - Add proper error handling and retry logic
  - Separate sync concerns from main transaction service
  - Include sync status tracking and conflict resolution
  - _Requirements: 1.3, 2.5_

- [ ] 2.5 Refactor Transaction Service to Remove Mixed Concerns
  - Remove direct database access from `TransactionService`
  - Extract CRUD operations to repository
  - Move analytics to dedicated analytics service
  - Keep only business logic in service layer
  - Update all method calls to use repository pattern
  - _Requirements: 1.2, 2.3, 2.5_

- [ ] 3. Refactor Notifications Module Service Architecture
  - Split oversized notification service into focused services
  - Implement proper repository pattern for notifications
  - Create reactive controller with stream management
  - Separate concerns: repository, analytics, sync, validation
  - _Requirements: 1.1, 1.2, 2.3, 2.4, 2.5_

- [ ] 3.1 Create Notifications Repository with Pure Reactive Streams
  - Extract data access logic from `NotificationService`
  - Create `NotificationsRepository` with stream-only operations
  - Implement `watchAllNotifications()` with reactive updates
  - Add CRUD methods: `saveNotification()`, `deleteNotification()`, `markAsRead()`
  - Remove business logic and error handling from repository
  - _Requirements: 1.1, 2.4, 5.4_

- [ ] 3.2 Split Notification Service into Focused Services
  - Create `NotificationValidationService` for validation logic
  - Create `NotificationSyncService` for offline/online sync
  - Create `NotificationAnalyticsService` for metrics calculation
  - Keep core orchestration in main `NotificationService`
  - Remove mixed responsibilities from single service
  - _Requirements: 2.3, 2.5_

- [ ] 3.3 Implement Notifications Controller with Dual Streams
  - Create `NotificationsController` following established pattern
  - Implement separate streams for filtered and unfiltered notifications
  - Add proper state management with loading/error states
  - Include filter application at database level
  - Calculate analytics on complete unfiltered dataset
  - _Requirements: 1.2, 2.3, 5.5_

- [ ] 3.4 Extract Notification Analytics to Dedicated Service
  - Move analytics calculations from main service to `NotificationAnalyticsService`
  - Implement pure calculation functions with no state
  - Add metrics: unread count, category distribution, priority analysis
  - Use single-pass algorithms for O(n) efficiency
  - Create immutable result objects
  - _Requirements: 2.5, 5.4_

## Phase 2: Medium Priority Module Refactoring (Weeks 4-5)

- [ ] 4. Refactor Farm Setup Repository to Remove Business Logic
  - Extract business logic from oversized repository
  - Create focused services for different concerns
  - Implement proper error handling separation
  - Reduce repository complexity to pure data access
  - _Requirements: 1.1, 1.2, 2.3, 2.4_

- [ ] 4.1 Extract Business Logic from Farm Setup Repository
  - Move validation logic to `FarmSetupValidationService`
  - Extract backup operations to `FarmBackupService`
  - Move user management to `FarmUserService`
  - Keep only CRUD operations in repository
  - Remove complex business rules from data layer
  - _Requirements: 1.2, 2.3, 2.5_

```dart
// BEFORE: farm_setup_repository.dart (MAJOR VIOLATION)
class FarmSetupRepository extends ChangeNotifier { // ❌ Repository should NOT extend ChangeNotifier
  static final Logger _logger = Logger('FarmSetupRepository'); // ❌ No logging in repository
  
  Future<FarmIsar?> getCurrentFarm() async {
    try { // ❌ No error handling in repository
      debugPrint('🏠 [FARM_REPO_DEBUG] getCurrentFarm() called'); // ❌ No debug prints
      final farm = await _isar.farmIsars.where().findFirst();
      if (farm != null) {
        debugPrint('✅ [FARM_REPO_DEBUG] Found current farm: ${farm.name}');
      } else {
        debugPrint('⚠️ [FARM_REPO_DEBUG] No current farm found in database');
      }
      return farm;
    } catch (e) {
      debugPrint('❌ [FARM_REPO_DEBUG] Error getting current farm: $e');
      _logger.severe('Error getting current farm: $e'); // ❌ No logging
      throw DatabaseException('Failed to retrieve current farm', e.toString()); // ❌ No custom exceptions
    }
  }
  
  /// Validate breed category // ❌ Business logic in repository
  Future<void> _validateBreedCategory(BreedCategoryIsar category) async {
    if (category.name == null || category.name!.isEmpty) {
      throw ValidationException('Breed category name is required');
    }
    // Complex validation logic here...
  }
  
  /// Create a backup using the configured storage provider // ❌ Complex business logic
  Future<CloudBackupResult> createBackup() async {
    try {
      final settings = await getBackupSettings();
      final farmId = settings.farmBusinessId ?? await getCurrentFarmId();
      // Complex backup logic here...
    } catch (e) {
      _logger.severe('Error creating backup: $e');
      return CloudBackupResult.error('Error creating backup: $e');
    }
  }
}

// AFTER: farm_setup_repository.dart (PURE REACTIVE PATTERN)
class FarmSetupRepository {
  final IsarService _isarService;
  
  // ✅ Public constructor with explicit dependency injection
  FarmSetupRepository(this._isarService);
  
  // ✅ Getter for Isar instance
  Isar get _isar => _isarService.isar;
  
  //=== REACTIVE FARM STREAMS ===//
  
  /// Watches all farms with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<FarmIsar>> watchAllFarms() {
    return _isar.farmIsars.where().watch(fireImmediately: true);
  }
  
  /// Watches all breed categories with reactive updates
  Stream<List<BreedCategoryIsar>> watchAllBreedCategories() {
    return _isar.breedCategoryIsars.where().watch(fireImmediately: true);
  }
  
  //=== FARM CRUD ===//
  
  /// Save (add or update) a farm using Isar's native upsert
  Future<void> saveFarm(FarmIsar farm) async {
    await _isar.writeTxn(() async {
      await _isar.farmIsars.put(farm);
    });
  }
  
  /// Delete a farm by its Isar ID
  Future<void> deleteFarm(int farmId) async {
    await _isar.writeTxn(() async {
      await _isar.farmIsars.delete(farmId);
    });
  }
  
  //=== QUERY METHODS FOR ANALYTICS AND VALIDATION ===//
  
  /// Get all farms (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<FarmIsar>> getAllFarms() async {
    return await _isar.farmIsars.where().findAll();
  }
  
  /// Get farm by business ID (for validation and navigation)
  Future<FarmIsar?> getFarmByBusinessId(String businessId) async {
    return await _isar.farmIsars.getByBusinessId(businessId);
  }
  
  /// Get current farm (first farm in database)
  Future<FarmIsar?> getCurrentFarm() async {
    return await _isar.farmIsars.where().findFirst();
  }
  
  // ❌ NO error handling - exceptions bubble up naturally
  // ❌ NO logging - pure data access only
  // ❌ NO business logic - moved to services
  // ❌ NO validation - moved to validation service
  // ❌ NO complex operations - moved to dedicated services
}
```

- [ ] 4.2 Create Focused Farm Setup Services
  - Implement `FarmSetupValidationService` for data validation
  - Create `FarmBackupService` for backup/restore operations
  - Add `FarmUserService` for user management
  - Separate concerns into single-responsibility services
  - Use dependency injection for service composition
  - _Requirements: 2.3, 2.5_

- [ ] 4.3 Implement Proper Error Handling in Farm Setup Services
  - Remove error handling from repository layer
  - Add comprehensive error handling in service layer
  - Create custom exception types for different error scenarios
  - Implement proper logging in services (not repository)
  - Use consistent error handling patterns across services
  - _Requirements: 1.5, 2.3_

- [ ] 4.4 Reduce Farm Setup Repository Complexity
  - Simplify repository to pure CRUD operations
  - Remove complex query logic (move to services if needed)
  - Implement standard reactive stream patterns
  - Add proper indexing for performance
  - Follow cattle/weight repository pattern exactly
  - _Requirements: 1.1, 2.4, 5.4_

- [ ] 5. Complete Milk Module Standardization
  - Create missing controller following established pattern
  - Implement proper module structure
  - Add analytics service for milk production metrics
  - Complete integration with Events module
  - _Requirements: 1.1, 1.2, 2.3, 2.4, 2.5_

- [ ] 5.1 Create Milk Controller with Dual-Stream Pattern
  - Implement `MilkController` following cattle controller pattern
  - Add separate streams for filtered and unfiltered milk records
  - Implement proper state management and error handling
  - Include filter application with database-level queries
  - Calculate analytics on complete unfiltered dataset
  - _Requirements: 1.2, 2.3, 5.5_

- [ ] 5.2 Implement Milk Analytics Service
  - Create `MilkAnalyticsService` with pure calculation functions
  - Add production metrics: daily average, monthly totals, trends
  - Implement cattle-specific milk production analysis
  - Use single-pass algorithms for efficiency
  - Create immutable analytics result objects
  - _Requirements: 2.5, 5.4_

- [ ] 5.3 Complete Milk Module Structure
  - Add missing directories: `controllers/`, `tabs/`, `details/`
  - Create `milk_screen.dart` following standard pattern
  - Implement `milk_records_tab.dart` and `milk_analytics_tab.dart`
  - Add `milk_form_dialog.dart` for data entry
  - Follow exact structure of cattle/weight modules
  - _Requirements: 1.4, 2.2_

- [ ] 5.4 Fix Milk Event Integration Service
  - Remove commented code from `MilkEventIntegration`
  - Implement proper event creation for milk records
  - Add integration with Events module following established patterns
  - Use dependency injection for EventsRepository
  - Test cross-module event creation
  - _Requirements: 2.3, 2.5_

## Phase 3: Low Priority Module Adjustments (Week 6)

- [ ] 6. Simplify Events Module Complexity
  - Reduce controller complexity while maintaining functionality
  - Simplify repository query methods
  - Optimize performance-heavy operations
  - Maintain compliance with established patterns
  - _Requirements: 1.1, 1.2, 2.4_

- [ ] 6.1 Simplify Events Controller Complexity
  - Reduce the size of `EventsController` by extracting helper methods
  - Simplify filter state management
  - Remove redundant code and consolidate similar methods
  - Maintain dual-stream pattern and analytics separation
  - Optimize stream management for better performance
  - _Requirements: 1.2, 2.3_

- [ ] 6.2 Optimize Events Repository Query Methods
  - Simplify complex query building in `EventsRepository`
  - Remove redundant query methods
  - Optimize database queries for better performance
  - Maintain reactive stream functionality
  - Keep repository pure and logic-free
  - _Requirements: 1.1, 2.4_

- [ ] 6.3 Streamline Events Service Architecture
  - Consolidate similar services in Events module
  - Remove unused services and methods
  - Simplify service dependencies
  - Maintain separation of concerns
  - Follow single responsibility principle
  - _Requirements: 2.3, 2.5_

- [ ] 7. Align Health Module with Established Patterns
  - Fix minor pattern deviations in Health module
  - Ensure consistent stream management
  - Verify analytics calculation on unfiltered data
  - Update any non-compliant code patterns
  - _Requirements: 1.1, 1.2, 2.3, 2.4, 2.5_

- [ ] 7.1 Verify Health Repository Pattern Compliance
  - Check `HealthRepository` follows cattle/weight pattern exactly
  - Ensure stream-only operations with no business logic
  - Verify proper dependency injection
  - Confirm CRUD methods use Isar's native upsert
  - Test reactive stream functionality
  - _Requirements: 1.1, 2.4, 5.4_

- [ ] 7.2 Update Health Controller Stream Management
  - Verify dual-stream pattern implementation
  - Ensure analytics calculated on unfiltered data only
  - Check filter application at database level
  - Confirm proper state management
  - Test stream disposal in dispose method
  - _Requirements: 1.2, 2.3, 5.5_

- [ ] 7.3 Validate Health Analytics Service
  - Ensure `HealthAnalyticsService` is purely functional
  - Verify single-pass algorithm efficiency
  - Check immutable result objects
  - Confirm no state in analytics service
  - Test calculation accuracy with test data
  - _Requirements: 2.5, 5.4_

- [ ] 8. Standardize User Account Module Streams
  - Update User Account module to use consistent stream patterns
  - Align authentication flow with established patterns
  - Ensure proper dependency injection throughout
  - Fix any mixed pattern usage
  - _Requirements: 1.1, 1.2, 1.3, 2.3_

- [ ] 8.1 Update User Account Controllers
  - Align `AuthController` and `UserProfileController` with standard pattern
  - Implement consistent stream management
  - Add proper state management with ControllerState enum
  - Ensure dependency injection follows established pattern
  - Remove any direct database access
  - _Requirements: 1.2, 1.3, 2.3_

- [ ] 8.2 Standardize User Account Services
  - Update `AuthService` and related services to follow patterns
  - Ensure proper separation of concerns
  - Implement consistent error handling
  - Use standard dependency injection
  - Remove mixed responsibilities
  - _Requirements: 1.3, 2.3, 2.5_

- [ ] 9. Complete Help Module Standardization
  - Expand Help module to follow standard structure
  - Add missing components for consistency
  - Implement proper patterns even for simple modules
  - Ensure future extensibility
  - _Requirements: 1.4, 2.2_

- [ ] 9.1 Expand Help Module Structure
  - Add missing directories: `controllers/`, `services/`, `models/`
  - Create `HelpController` following standard pattern
  - Add `HelpRepository` for help content management
  - Implement proper module structure for consistency
  - Prepare for future help content features
  - _Requirements: 1.4, 2.2_

## Phase 4: File Cleanup and Validation (Week 7)

- [ ] 10. Comprehensive File Cleanup
  - Remove unused, duplicate, and obsolete files
  - Clean up import statements across all modules
  - Remove dead code and commented sections
  - Preserve all documentation and README files
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 10.1 Identify and Remove Unused Files
  - Scan all Dashboard modules for unused files
  - Remove duplicate implementations
  - Delete obsolete service files
  - Keep all `.kiro/` directory files intact
  - Preserve all README.md and documentation files
  - _Requirements: 4.1, 4.3, 4.4_

- [ ] 10.2 Clean Up Import Statements
  - Remove unused imports across all Dart files
  - Organize imports following Dart conventions
  - Remove redundant dependencies
  - Ensure all required imports are present
  - Use IDE tools for automated cleanup where possible
  - _Requirements: 4.5_

- [ ] 10.3 Remove Dead Code and Comments
  - Remove commented-out code blocks
  - Delete unused methods and classes
  - Remove debug print statements (keep essential logging)
  - Clean up TODO comments that are no longer relevant
  - Preserve important documentation comments
  - _Requirements: 4.1, 4.2_

- [ ] 11. Testing and Validation
  - Create comprehensive tests for refactored modules
  - Validate pattern compliance across all modules
  - Test performance improvements
  - Verify backward compatibility
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 11.1 Create Unit Tests for Refactored Repositories
  - Write tests for all repository CRUD operations
  - Test reactive stream functionality
  - Verify query methods return correct data
  - Mock Isar database for isolated testing
  - Ensure 100% test coverage for repositories
  - _Requirements: 6.1, 6.2_

```dart
// Example: Repository Unit Test Template
group('FarmSetupRepository Tests', () {
  late FarmSetupRepository repository;
  late MockIsarService mockIsarService;
  late MockIsar mockIsar;
  
  setUp(() {
    mockIsarService = MockIsarService();
    mockIsar = MockIsar();
    when(mockIsarService.isar).thenReturn(mockIsar);
    repository = FarmSetupRepository(mockIsarService);
  });
  
  group('Stream Operations', () {
    test('should watch all farms with reactive updates', () async {
      // Arrange
      final farms = [
        FarmIsar()..businessId = 'farm1'..name = 'Test Farm 1',
        FarmIsar()..businessId = 'farm2'..name = 'Test Farm 2',
      ];
      final streamController = StreamController<List<FarmIsar>>();
      when(mockIsar.farmIsars.where().watch(fireImmediately: true))
          .thenAnswer((_) => streamController.stream);
      
      // Act
      final result = repository.watchAllFarms();
      streamController.add(farms);
      
      // Assert
      expect(result, emits(farms));
      verify(mockIsar.farmIsars.where().watch(fireImmediately: true)).called(1);
    });
    
    test('should watch breed categories with reactive updates', () async {
      // Arrange
      final categories = [
        BreedCategoryIsar()..businessId = 'breed1'..name = 'Holstein',
      ];
      when(mockIsar.breedCategoryIsars.where().watch(fireImmediately: true))
          .thenAnswer((_) => Stream.value(categories));
      
      // Act & Assert
      expect(repository.watchAllBreedCategories(), emits(categories));
    });
  });
  
  group('CRUD Operations', () {
    test('should save farm using Isar transaction', () async {
      // Arrange
      final farm = FarmIsar()..businessId = 'farm1'..name = 'Test Farm';
      when(mockIsar.writeTxn(any)).thenAnswer((_) async {});
      
      // Act
      await repository.saveFarm(farm);
      
      // Assert
      verify(mockIsar.writeTxn(any)).called(1);
      verify(mockIsar.farmIsars.put(farm)).called(1);
    });
    
    test('should delete farm by ID', () async {
      // Arrange
      const farmId = 123;
      when(mockIsar.writeTxn(any)).thenAnswer((_) async {});
      
      // Act
      await repository.deleteFarm(farmId);
      
      // Assert
      verify(mockIsar.writeTxn(any)).called(1);
      verify(mockIsar.farmIsars.delete(farmId)).called(1);
    });
  });
  
  group('Query Methods', () {
    test('should get all farms for analytics', () async {
      // Arrange
      final farms = [FarmIsar()..businessId = 'farm1'..name = 'Test Farm'];
      when(mockIsar.farmIsars.where().findAll())
          .thenAnswer((_) async => farms);
      
      // Act
      final result = await repository.getAllFarms();
      
      // Assert
      expect(result, equals(farms));
      verify(mockIsar.farmIsars.where().findAll()).called(1);
    });
    
    test('should get farm by business ID', () async {
      // Arrange
      const businessId = 'farm1';
      final farm = FarmIsar()..businessId = businessId..name = 'Test Farm';
      when(mockIsar.farmIsars.getByBusinessId(businessId))
          .thenAnswer((_) async => farm);
      
      // Act
      final result = await repository.getFarmByBusinessId(businessId);
      
      // Assert
      expect(result, equals(farm));
      verify(mockIsar.farmIsars.getByBusinessId(businessId)).called(1);
    });
  });
  
  group('Error Handling', () {
    test('should let exceptions bubble up naturally', () async {
      // Arrange
      when(mockIsar.farmIsars.where().findAll())
          .thenThrow(Exception('Database error'));
      
      // Act & Assert
      expect(
        () => repository.getAllFarms(),
        throwsA(isA<Exception>()),
      );
    });
  });
  
  group('Pattern Compliance', () {
    test('should have no business logic in repository', () {
      // This test verifies that repository methods are simple CRUD operations
      // No complex business logic should be present
      final repositoryMethods = reflectClass(FarmSetupRepository)
          .instanceMembers
          .values
          .where((method) => !method.isPrivate);
      
      for (final method in repositoryMethods) {
        // Verify method complexity is low (simple CRUD only)
        // This would be implemented with static analysis tools
      }
    });
    
    test('should use explicit dependency injection', () {
      // Verify constructor takes IsarService parameter
      expect(repository, isA<FarmSetupRepository>());
      // Verify no GetIt.instance calls in repository methods
    });
  });
});
```

- [ ] 11.2 Create Controller Integration Tests
  - Test dual-stream functionality
  - Verify filter application works correctly
  - Test analytics calculation accuracy
  - Validate state management transitions
  - Test error handling scenarios
  - _Requirements: 6.2, 6.3_

```dart
// Example: Controller Integration Test Template
group('FarmSetupController Integration Tests', () {
  late FarmSetupController controller;
  late MockFarmSetupRepository mockRepository;
  late MockFarmSetupService mockService;
  late StreamController<List<FarmIsar>> farmsStreamController;
  
  setUp(() {
    mockRepository = MockFarmSetupRepository();
    mockService = MockFarmSetupService();
    farmsStreamController = StreamController<List<FarmIsar>>();
    
    when(mockRepository.watchAllFarms())
        .thenAnswer((_) => farmsStreamController.stream);
    
    controller = FarmSetupController(mockRepository, mockService);
  });
  
  tearDown(() {
    controller.dispose();
    farmsStreamController.close();
  });
  
  group('Dual-Stream Functionality', () {
    test('should separate unfiltered and filtered data correctly', () async {
      // Arrange
      final allFarms = [
        FarmIsar()..businessId = 'farm1'..name = 'Farm A'..location = 'Location A',
        FarmIsar()..businessId = 'farm2'..name = 'Farm B'..location = 'Location B',
        FarmIsar()..businessId = 'farm3'..name = 'Farm C'..location = 'Location A',
      ];
      
      // Act
      farmsStreamController.add(allFarms);
      await Future.delayed(Duration.zero); // Allow stream to emit
      
      // Assert - Unfiltered data should contain all farms
      expect(controller.unfilteredFarms, equals(allFarms));
      expect(controller.unfilteredFarms.length, equals(3));
      
      // Initially, filtered data should equal unfiltered data
      expect(controller.farms, equals(allFarms));
      
      // Apply filter
      controller.applyFilters(FarmFilter(location: 'Location A'));
      await Future.delayed(Duration.zero);
      
      // Filtered data should be subset, unfiltered should remain complete
      expect(controller.farms.length, equals(2)); // Only Location A farms
      expect(controller.unfilteredFarms.length, equals(3)); // Still all farms
      expect(controller.unfilteredFarms, equals(allFarms)); // Unchanged
    });
    
    test('should calculate analytics on unfiltered data only', () async {
      // Arrange
      final allFarms = [
        FarmIsar()..businessId = 'farm1'..location = 'Location A',
        FarmIsar()..businessId = 'farm2'..location = 'Location B',
        FarmIsar()..businessId = 'farm3'..location = 'Location A',
      ];
      
      // Act
      farmsStreamController.add(allFarms);
      await Future.delayed(Duration.zero);
      
      // Apply filter that reduces visible farms
      controller.applyFilters(FarmFilter(location: 'Location A'));
      await Future.delayed(Duration.zero);
      
      // Assert - Analytics should reflect ALL farms, not just filtered
      expect(controller.analytics.totalFarms, equals(3)); // All farms
      expect(controller.farms.length, equals(2)); // Filtered farms
      
      // Analytics should not change when filters are applied
      final analyticsBeforeFilter = controller.analytics;
      controller.applyFilters(FarmFilter(location: 'Location B'));
      await Future.delayed(Duration.zero);
      
      expect(controller.analytics.totalFarms, equals(analyticsBeforeFilter.totalFarms));
    });
  });
  
  group('State Management', () {
    test('should transition through states correctly', () async {
      // Arrange
      final stateChanges = <ControllerState>[];
      controller.addListener(() {
        stateChanges.add(controller.state);
      });
      
      // Act
      expect(controller.state, equals(ControllerState.loading)); // Initial state
      
      farmsStreamController.add([FarmIsar()..businessId = 'farm1']);
      await Future.delayed(Duration.zero);
      
      // Assert
      expect(controller.state, equals(ControllerState.loaded));
      expect(stateChanges, contains(ControllerState.loaded));
    });
    
    test('should handle error states correctly', () async {
      // Arrange
      final stateChanges = <ControllerState>[];
      final errorMessages = <String?>[];
      
      controller.addListener(() {
        stateChanges.add(controller.state);
        errorMessages.add(controller.errorMessage);
      });
      
      // Act
      farmsStreamController.addError(Exception('Database connection failed'));
      await Future.delayed(Duration.zero);
      
      // Assert
      expect(controller.state, equals(ControllerState.error));
      expect(controller.errorMessage, isNotNull);
      expect(controller.errorMessage, contains('Database connection failed'));
    });
  });
  
  group('Filter Application', () {
    test('should apply filters at database level', () async {
      // Arrange
      final mockFilteredQuery = MockQuery<FarmIsar>();
      when(mockRepository.buildFilteredQuery(any))
          .thenReturn(mockFilteredQuery);
      when(mockFilteredQuery.watch(fireImmediately: true))
          .thenAnswer((_) => Stream.value([]));
      
      // Act
      controller.applyFilters(FarmFilter(location: 'Test Location'));
      
      // Assert
      verify(mockRepository.buildFilteredQuery(any)).called(1);
      verify(mockFilteredQuery.watch(fireImmediately: true)).called(1);
    });
    
    test('should clear filters correctly', () async {
      // Arrange
      final allFarms = [FarmIsar()..businessId = 'farm1'];
      farmsStreamController.add(allFarms);
      await Future.delayed(Duration.zero);
      
      // Apply filter
      controller.applyFilters(FarmFilter(location: 'Test'));
      await Future.delayed(Duration.zero);
      
      // Act
      controller.clearFilters();
      await Future.delayed(Duration.zero);
      
      // Assert
      expect(controller.farms, equals(controller.unfilteredFarms));
    });
  });
  
  group('Business Operations', () {
    test('should create farm through service layer', () async {
      // Arrange
      final farm = FarmIsar()..businessId = 'farm1'..name = 'New Farm';
      when(mockService.createFarm(
        name: 'New Farm',
        location: 'Test Location',
      )).thenAnswer((_) async => Result.success(farm));
      
      // Act
      await controller.createFarm(
        name: 'New Farm',
        location: 'Test Location',
      );
      
      // Assert
      verify(mockService.createFarm(
        name: 'New Farm',
        location: 'Test Location',
      )).called(1);
      expect(controller.state, isNot(ControllerState.error));
    });
    
    test('should handle service errors correctly', () async {
      // Arrange
      when(mockService.createFarm(
        name: 'Invalid Farm',
        location: '',
      )).thenAnswer((_) async => Result.failure('Location is required'));
      
      // Act
      await controller.createFarm(
        name: 'Invalid Farm',
        location: '',
      );
      
      // Assert
      expect(controller.state, equals(ControllerState.error));
      expect(controller.errorMessage, equals('Location is required'));
    });
  });
  
  group('Memory Management', () {
    test('should dispose stream subscriptions properly', () {
      // Arrange
      final controller = FarmSetupController(mockRepository, mockService);
      
      // Act
      controller.dispose();
      
      // Assert
      // Verify that stream subscriptions are cancelled
      // This would be verified through stream subscription tracking
      expect(controller.state, equals(ControllerState.loading));
    });
    
    test('should not leak memory with multiple controllers', () async {
      // Arrange
      final controllers = <FarmSetupController>[];
      
      // Act
      for (int i = 0; i < 100; i++) {
        final controller = FarmSetupController(mockRepository, mockService);
        controllers.add(controller);
      }
      
      // Dispose all controllers
      for (final controller in controllers) {
        controller.dispose();
      }
      
      // Assert
      // Memory usage should return to baseline
      // This would require memory profiling tools in a real test
      expect(controllers.length, equals(100));
    });
  });
});
```

- [ ] 11.3 Validate Analytics Service Accuracy
  - Create comprehensive test data sets
  - Test all analytics calculations
  - Verify single-pass algorithm efficiency
  - Test edge cases and empty data scenarios
  - Benchmark performance improvements
  - _Requirements: 6.1, 6.4_

- [ ] 11.4 Performance Testing and Optimization
  - Benchmark stream performance improvements
  - Test memory usage with large datasets
  - Verify database query optimization
  - Test UI responsiveness with filtered data
  - Measure analytics calculation speed
  - _Requirements: 6.4, 6.5_

- [ ] 12. Final Pattern Compliance Verification
  - Audit all modules for pattern compliance
  - Create compliance checklist and verify each module
  - Document any remaining deviations with justification
  - Update architecture documentation
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 12.1 Create Pattern Compliance Checklist
  - Define specific criteria for each pattern requirement
  - Create automated checks where possible
  - Document the standard patterns clearly
  - Include code examples for each pattern
  - Create validation tools for future development
  - _Requirements: 7.1, 7.2, 7.3_

- [ ] 12.2 Audit All Modules for Compliance
  - Check each module against the compliance checklist
  - Verify repository patterns match cattle/weight exactly
  - Confirm controller dual-stream implementation
  - Validate analytics service purity
  - Test dependency injection consistency
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 12.3 Update Documentation and Architecture Guides
  - Update module documentation with new patterns
  - Create developer guidelines for future development
  - Document the refactoring changes and improvements
  - Update API documentation where needed
  - Create pattern examples for reference
  - _Requirements: 7.5_

## 📚 Final Documentation Requirements

### Architecture Documentation Updates
Create comprehensive documentation covering:

1. **Reactive Repository Pattern Guide**
```dart
/// STANDARD REPOSITORY PATTERN - FOLLOW EXACTLY
/// 
/// ✅ DO: Stream-only operations with fireImmediately: true
/// ✅ DO: Simple CRUD operations using Isar's native upsert
/// ✅ DO: Explicit dependency injection through constructor
/// ✅ DO: Query methods returning Future<List> for analytics
/// 
/// ❌ DON'T: Add business logic or validation
/// ❌ DON'T: Include error handling or logging
/// ❌ DON'T: Extend ChangeNotifier or any other class
/// ❌ DON'T: Use GetIt.instance inside repository methods
/// 
/// Example Implementation:
class ExampleRepository {
  final IsarService _isarService;
  ExampleRepository(this._isarService); // ✅ Explicit DI
  
  Isar get _isar => _isarService.isar;
  
  Stream<List<ExampleIsar>> watchAllRecords() {
    return _isar.exampleIsars.where().watch(fireImmediately: true); // ✅ Reactive stream
  }
  
  Future<void> saveRecord(ExampleIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.exampleIsars.put(record); // ✅ Simple CRUD
    });
  }
}
```

2. **Dual-Stream Controller Pattern Guide**
```dart
/// STANDARD CONTROLLER PATTERN - CRITICAL FOR ANALYTICS ACCURACY
/// 
/// ✅ DO: Separate unfiltered data (analytics) from filtered data (UI)
/// ✅ DO: Calculate analytics ONLY on unfiltered data
/// ✅ DO: Use individual stream subscriptions for better error handling
/// ✅ DO: Implement proper state management with ControllerState enum
/// ✅ DO: Dispose all stream subscriptions in dispose() method
/// 
/// ❌ DON'T: Calculate analytics on filtered data
/// ❌ DON'T: Mix filtered and unfiltered data streams
/// ❌ DON'T: Access repositories directly from UI components
/// 
/// Critical Data Separation:
class ExampleController extends ChangeNotifier {
  // CRITICAL: Separate data for different purposes
  List<ExampleIsar> _unfilteredData = []; // ✅ For analytics - NEVER filtered
  List<ExampleIsar> _filteredData = [];   // ✅ For UI display - user filtered
  
  // Public getters maintain separation
  List<ExampleIsar> get data => List.unmodifiable(_filteredData);
  List<ExampleIsar> get unfilteredData => List.unmodifiable(_unfilteredData);
  
  // Analytics ALWAYS calculated on unfiltered data
  void _calculateAnalytics() {
    _analyticsResult = AnalyticsService.calculate(_unfilteredData); // ✅ Unfiltered only
  }
}
```

3. **Pure Analytics Service Pattern Guide**
```dart
/// STANDARD ANALYTICS SERVICE PATTERN - PURE FUNCTIONS ONLY
/// 
/// ✅ DO: Use static methods for all calculations
/// ✅ DO: Implement single-pass O(n) algorithms
/// ✅ DO: Return immutable result objects
/// ✅ DO: Use accumulator pattern for efficiency
/// 
/// ❌ DON'T: Store any state in analytics services
/// ❌ DON'T: Include instance methods or variables
/// ❌ DON'T: Access databases or external services
/// ❌ DON'T: Include business logic beyond calculations
/// 
/// Example Implementation:
class ExampleAnalyticsService {
  /// Main calculation - single entry point, O(n) efficiency
  static ExampleAnalyticsResult calculate(List<ExampleIsar> records) {
    if (records.isEmpty) return ExampleAnalyticsResult.empty;
    
    final accumulator = _ExampleAnalyticsAccumulator();
    for (final record in records) {
      accumulator.process(record); // Single pass through data
    }
    return accumulator.toResult(); // Immutable result
  }
  
  // ❌ NO instance variables
  // ❌ NO state management
  // ❌ NO database access
}
```

### Developer Guidelines Document
Create `docs/DEVELOPMENT_GUIDELINES.md` with:

- **Module Creation Checklist**: Step-by-step guide for new modules
- **Code Review Checklist**: Pattern compliance verification
- **Testing Requirements**: Minimum test coverage and types
- **Performance Standards**: Benchmarks and optimization guidelines
- **Common Pitfalls**: Mistakes to avoid with examples

### Migration Guide
Create `docs/REFACTORING_MIGRATION_GUIDE.md` with:

- **Before/After Comparisons**: Visual examples of pattern changes
- **Breaking Changes**: List of API changes and migration steps
- **Rollback Procedures**: How to revert changes if needed
- **Testing Validation**: How to verify refactoring success

### Pattern Compliance Tools
Create automated validation tools:

- **Static Analysis Rules**: Custom lint rules for pattern enforcement
- **CI/CD Integration**: Automated pattern compliance checking
- **Code Generation Templates**: IDE templates for new modules
- **Performance Monitoring**: Automated performance regression detection