import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:logging/logging.dart';

// Core services
import '../services/database/isar_service.dart';
import '../services/logging_service.dart';
import '../services/auto_backup_service.dart';
import 'default_data_seeder.dart';
import '../Dashboard/Farm Setup/services/cloud_backup_service.dart';

// Repositories (consistent naming convention)
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
import '../Dashboard/Cattle/services/cattle_repository.dart';
import '../Dashboard/Cattle/services/cattle_insights_service.dart';
import '../Dashboard/Health/services/health_repository.dart';
import '../Dashboard/Health/services/health_insights_service.dart';
import '../Dashboard/Breeding/services/breeding_repository.dart';
import '../Dashboard/Breeding/services/breeding_insights_service.dart';

import '../Dashboard/Milk Records/services/milk_repository.dart';
import '../Dashboard/Milk Records/services/milk_insights_service.dart';
import '../Dashboard/Weight/services/weight_repository.dart';
import '../Dashboard/Weight/services/weight_insights_service.dart';
import '../Dashboard/Events/services/events_repository.dart';
import '../Dashboard/Transactions/services/transactions_repository.dart';
// TEMPORARILY DISABLED - Reports module imports due to GetX dependency issues
// import '../Dashboard/Reports/services/reports_service.dart';
// import '../Dashboard/Reports/services/reports_cache_service.dart';
// import '../Dashboard/Reports/services/sharing_service.dart';
import '../Dashboard/Notifications/services/notification_repository.dart';
import '../Dashboard/Notifications/services/notification_settings_repository.dart';
import '../Dashboard/Notifications/services/push_notification_service.dart';
import '../Dashboard/Notifications/services/offline_notification_service.dart';
import '../Dashboard/Notifications/services/notification_error_handler.dart';
import '../Dashboard/Notifications/services/notification_service.dart';
import '../Dashboard/Notifications/services/notification_automation_service.dart';
import '../Dashboard/Notifications/services/notification_lifecycle_service.dart';
import '../Dashboard/Notifications/services/event_notification_integration_service.dart';
// import '../Dashboard/Events/services/event_repository.dart'; // File removed

// Controllers are now managed by Provider, not GetIt
import '../services/validation/validation_service.dart';
import '../Dashboard/User Account/services/user_repository.dart';
import '../Dashboard/User Account/services/auth_service.dart';
import '../Dashboard/User Account/services/biometric_auth_service.dart';
import '../Dashboard/User Account/services/email_service.dart';
import '../Dashboard/User Account/services/security_service.dart';
import '../Dashboard/User Account/services/data_ownership_service.dart';
import '../Dashboard/User Account/services/error_handling_service.dart';
import '../Dashboard/Cattle/services/cattle_ownership_service.dart';

/// Centralized dependency injection setup
/// Replaces the God Object pattern with clean, testable DI
class DependencyInjection {
  static final Logger _logger = Logger('DependencyInjection');
  static bool _isInitialized = false;

  /// Initialize all dependencies
  /// This replaces DatabaseHelper and IsarInitializer
  static Future<void> initDependencies([LoggingService? existingLoggingService]) async {
    if (_isInitialized) {
      _logger.info('Dependencies already initialized');
      return;
    }

    try {
      _logger.info('Initializing dependencies...');
      final getIt = GetIt.instance;

      // 1. Register core services first (including LoggingService if provided)
      await _registerCoreServices(getIt, existingLoggingService);

      // 2. Register all repositories/handlers
      await _registerRepositories(getIt);

      // 3. Register utility services (like DefaultDataSeeder)
      // Note: Controllers are now managed by Provider, not GetIt
      registerUtilityServices(getIt);

      _isInitialized = true;
      _logger.info('Dependencies initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing dependencies: $e');
      rethrow;
    }
  }

  /// Register core services (LoggingService, IsarService, Isar instance)
  static Future<void> _registerCoreServices(GetIt getIt, [LoggingService? existingLoggingService]) async {
    _logger.info('Registering core services...');

    // Register LoggingService as singleton first (needed by other services)
    if (!getIt.isRegistered<LoggingService>()) {
      final loggingService = existingLoggingService ?? LoggingService();
      if (existingLoggingService == null) {
        loggingService.setupLogging();
        loggingService.logUncaughtExceptions();
      }
      getIt.registerSingleton<LoggingService>(loggingService);
      _logger.info('LoggingService registered');
    }

    // Register IsarService as singleton with proper DI pattern
    if (!getIt.isRegistered<IsarService>()) {
      // Create the instance, initialize it, then register it
      final isarService = IsarService();
      await isarService.initialize(); // Initialize before registering
      getIt.registerSingleton<IsarService>(isarService);
      _logger.info('IsarService registered');
    }

    // Register Isar instance for direct access
    if (!getIt.isRegistered<Isar>()) {
      final isarService = getIt<IsarService>();
      getIt.registerSingleton<Isar>(isarService.isar);
      _logger.info('Isar instance registered');
    }

    // Register CloudBackupService as singleton (initialize without auto-authentication)
    if (!getIt.isRegistered<CloudBackupService>()) {
      final cloudBackupService = CloudBackupService();
      // Initialize the service but don't trigger Google Drive authentication
      await cloudBackupService.initialize(
        isarService: getIt<IsarService>(),
        // authService will be created internally to avoid auto-authentication
      );
      getIt.registerSingleton<CloudBackupService>(cloudBackupService);
      _logger.info('CloudBackupService registered and initialized');
    }

    // Register AutoBackupService as singleton
    if (!getIt.isRegistered<AutoBackupService>()) {
      final autoBackupService = AutoBackupService();
      getIt.registerSingleton<AutoBackupService>(autoBackupService);
      _logger.info('AutoBackupService registered');
    }
  }

  /// Register all repositories as singletons
  static Future<void> _registerRepositories(GetIt getIt) async {
    _logger.info('Registering repositories...');

    // Farm Setup - with explicit dependencies
    if (!getIt.isRegistered<FarmSetupRepository>()) {
      getIt.registerSingleton<FarmSetupRepository>(
        FarmSetupRepository(
          getIt<IsarService>(),
          getIt<CloudBackupService>(),
        ),
      );
    }

    // Cattle - register CattleRepository with explicit dependency
    if (!getIt.isRegistered<CattleRepository>()) {
      getIt.registerSingleton<CattleRepository>(
        CattleRepository(getIt<IsarService>()),
      );
    }

    // Cattle Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<CattleInsightsService>()) {
      getIt.registerSingleton<CattleInsightsService>(
        CattleInsightsService(),
      );
    }

    // Health - with explicit dependency
    if (!getIt.isRegistered<HealthRepository>()) {
      getIt.registerSingleton<HealthRepository>(
        HealthRepository(getIt<IsarService>()),
      );
    }

    // Health Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<HealthInsightsService>()) {
      getIt.registerSingleton<HealthInsightsService>(
        HealthInsightsService(),
      );
    }

    // Breeding - with explicit dependency
    if (!getIt.isRegistered<BreedingRepository>()) {
      getIt.registerSingleton<BreedingRepository>(
        BreedingRepository(getIt<IsarService>()),
      );
    }

    // Breeding Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<BreedingInsightsService>()) {
      getIt.registerSingleton<BreedingInsightsService>(
        BreedingInsightsService(),
      );
    }



    // Milk - with explicit dependency
    if (!getIt.isRegistered<MilkRepository>()) {
      getIt.registerSingleton<MilkRepository>(
        MilkRepository(getIt<IsarService>()),
      );
    }

    // Milk Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<MilkInsightsService>()) {
      getIt.registerSingleton<MilkInsightsService>(
        MilkInsightsService(),
      );
    }

    // Weight - with explicit dependency
    if (!getIt.isRegistered<WeightRepository>()) {
      getIt.registerSingleton<WeightRepository>(
        WeightRepository(getIt<IsarService>()),
      );
    }

    // Events - with explicit dependency
    if (!getIt.isRegistered<EventsRepository>()) {
      getIt.registerSingleton<EventsRepository>(
        EventsRepository(getIt<IsarService>()),
      );
    }

    // Weight Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<WeightInsightsService>()) {
      getIt.registerSingleton<WeightInsightsService>(
        WeightInsightsService(),
      );
    }

    // Transactions - with explicit dependency
    if (!getIt.isRegistered<TransactionsRepository>()) {
      getIt.registerSingleton<TransactionsRepository>(
        TransactionsRepository(getIt<IsarService>()),
      );
    }

    // Reports - TEMPORARILY DISABLED due to GetX dependency issues
    // TODO: Refactor Reports module to use Provider + GetIt instead of GetX
    // if (!getIt.isRegistered<ReportsService>()) {
    //   getIt.registerSingleton<ReportsService>(
    //     ReportsService(),
    //   );
    // }
    //
    // if (!getIt.isRegistered<ReportsCacheService>()) {
    //   getIt.registerSingleton<ReportsCacheService>(
    //     ReportsCacheService(),
    //   );
    // }
    //
    // if (!getIt.isRegistered<SharingService>()) {
    //   getIt.registerSingleton<SharingService>(
    //     SharingService(),
    //   );
    // }

    // Notifications - with explicit dependencies
    if (!getIt.isRegistered<NotificationRepository>()) {
      getIt.registerSingleton<NotificationRepository>(
        NotificationRepository(getIt<IsarService>()),
      );
    }
    
    if (!getIt.isRegistered<NotificationSettingsRepository>()) {
      getIt.registerSingleton<NotificationSettingsRepository>(
        NotificationSettingsRepository(getIt<IsarService>()),
      );
    }
    
    if (!getIt.isRegistered<PushNotificationService>()) {
      getIt.registerSingleton<PushNotificationService>(
        PushNotificationService(getIt<NotificationSettingsRepository>()),
      );
    }
    
    if (!getIt.isRegistered<OfflineNotificationService>()) {
      getIt.registerSingleton<OfflineNotificationService>(
        OfflineNotificationService(
          getIt<IsarService>(),
          getIt<NotificationRepository>(),
        ),
      );
    }
    
    if (!getIt.isRegistered<NotificationErrorHandler>()) {
      getIt.registerSingleton<NotificationErrorHandler>(
        NotificationErrorHandler(
          getIt<NotificationRepository>(),
          getIt<NotificationSettingsRepository>(),
          getIt<PushNotificationService>(),
          getIt<OfflineNotificationService>(),
          (message) => debugPrint(message), // Replace with proper UI message handler
        ),
      );
    }
    
    if (!getIt.isRegistered<NotificationService>()) {
      getIt.registerSingleton<NotificationService>(
        NotificationService(
          getIt<NotificationRepository>(),
          getIt<NotificationSettingsRepository>(),
          getIt<PushNotificationService>(),
          getIt<OfflineNotificationService>(),
        ),
      );
    }
    
    if (!getIt.isRegistered<NotificationAutomationService>()) {
      getIt.registerSingleton<NotificationAutomationService>(
        NotificationAutomationService(
          getIt<NotificationRepository>(),
          getIt<NotificationSettingsRepository>(),
          getIt<CattleRepository>(),
          getIt<HealthRepository>(),
          getIt<WeightRepository>(),
          getIt<MilkRepository>(),
        ),
      );
    }
    
    // EventNotificationIntegrationService - currently disabled but registered for dependency resolution
    if (!getIt.isRegistered<EventNotificationIntegrationService>()) {
      getIt.registerSingleton<EventNotificationIntegrationService>(
        EventNotificationIntegrationService(),
      );
    }
    
    if (!getIt.isRegistered<NotificationLifecycleService>()) {
      getIt.registerSingleton<NotificationLifecycleService>(
        NotificationLifecycleService(
          getIt<NotificationService>(),
          getIt<PushNotificationService>(),
          getIt<OfflineNotificationService>(),
        ),
      );
    }



    // User Account - with explicit dependency
    if (!getIt.isRegistered<UserRepository>()) {
      getIt.registerSingleton<UserRepository>(
        UserRepository(),
      );
    }

    // Authentication Service - singleton for app-wide auth state
    if (!getIt.isRegistered<AuthService>()) {
      getIt.registerSingleton<AuthService>(
        AuthService(),
      );
    }

    // Biometric Authentication Service - singleton for biometric auth
    if (!getIt.isRegistered<BiometricAuthService>()) {
      getIt.registerSingleton<BiometricAuthService>(
        BiometricAuthService(),
      );
    }

    // Email Service - singleton for email operations
    if (!getIt.isRegistered<EmailService>()) {
      getIt.registerSingleton<EmailService>(
        EmailService(),
      );
    }

    // Security Service - singleton for security operations
    if (!getIt.isRegistered<SecurityService>()) {
      getIt.registerSingleton<SecurityService>(
        SecurityService(),
      );
    }

    // Data Ownership Service - singleton for access control
    if (!getIt.isRegistered<DataOwnershipService>()) {
      getIt.registerSingleton<DataOwnershipService>(
        DataOwnershipService(),
      );
    }

    // Error Handling Service - singleton for error management
    if (!getIt.isRegistered<ErrorHandlingService>()) {
      getIt.registerSingleton<ErrorHandlingService>(
        ErrorHandlingService(),
      );
    }

    // Cattle Ownership Service - with dependencies
    if (!getIt.isRegistered<CattleOwnershipService>()) {
      getIt.registerSingleton<CattleOwnershipService>(
        CattleOwnershipService(
          getIt<CattleRepository>(),
          getIt<DataOwnershipService>(),
          getIt<AuthService>(),
        ),
      );
    }

    // ValidationService - with explicit dependencies
    if (!getIt.isRegistered<ValidationService>()) {
      getIt.registerSingleton<ValidationService>(
        ValidationService(
          getIt<CattleRepository>(),
          getIt<FarmSetupRepository>(),
          getIt<UserRepository>(),
          // getIt<HealthRepository>(),
          // getIt<BreedingRepository>(),
          // getIt<TransactionsRepository>(),
        ),
      );
    }

    _logger.info('All repositories registered successfully');
  }

  // Controllers are now managed by Provider, not GetIt
  // This method is no longer needed

  /// Register utility services like DefaultDataSeeder
  static void registerUtilityServices(GetIt getIt) {
    _logger.info('Registering utility services...');

    // DefaultDataSeeder - with explicit dependencies (Pure DI pattern)
    if (!getIt.isRegistered<DefaultDataSeeder>()) {
      getIt.registerSingleton<DefaultDataSeeder>(
        DefaultDataSeeder(
          getIt<IsarService>(),
          getIt<FarmSetupRepository>(),
          // getIt<EventsRepository>(),
          getIt<TransactionsRepository>(),
          // getIt<SettingsRepository>(),
        ),
      );
    }

    _logger.info('All utility services registered successfully');
  }

  /// Reset all dependencies (useful for testing)
  static Future<void> reset() async {
    _logger.info('Resetting dependencies...');
    await GetIt.instance.reset();
    _isInitialized = false;
    _logger.info('Dependencies reset');
  }

  /// Check if dependencies are initialized
  static bool get isInitialized => _isInitialized;
}
