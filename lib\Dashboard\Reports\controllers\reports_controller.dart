
import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../services/reports_service.dart';
import '../services/reports_cache_service.dart';
import '../services/performance_monitor.dart';
import '../services/error_handler.dart';
import '../services/sharing_service.dart';

/// Reports Controller
///
/// Manages state for the unified reports system, handles data loading,
/// filtering, and export operations.
///
/// Follows Provider pattern with ChangeNotifier for reactive UI updates.
class ReportsController extends ChangeNotifier {
  final ReportsService _reportsService = ReportsService();
  final ReportsCacheService _cacheService = ReportsCacheService();
  final SharingService _sharingService = SharingService();

  // State management
  bool _isLoading = false;
  String _errorMessage = '';
  ReportData? _dashboardData;
  ReportData? _currentReportData;
  ReportType _selectedReportType = ReportType.dashboard;
  FilterState _currentFilter = const FilterState();

  // Getters
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  ReportData? get dashboardData => _dashboardData;
  ReportData? get currentReportData => _currentReportData;
  ReportType get selectedReportType => _selectedReportType;
  FilterState get currentFilter => _currentFilter;

  // Initialize controller - called when Provider creates the controller
  void initialize() {
    loadDashboard();
  }

  // Private setters that notify listeners
  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      notifyListeners();
    }
  }

  void _setErrorMessage(String value) {
    if (_errorMessage != value) {
      _errorMessage = value;
      notifyListeners();
    }
  }

  void _setDashboardData(ReportData? value) {
    if (_dashboardData != value) {
      _dashboardData = value;
      notifyListeners();
    }
  }

  void _setCurrentReportData(ReportData? value) {
    if (_currentReportData != value) {
      _currentReportData = value;
      notifyListeners();
    }
  }

  void _setSelectedReportType(ReportType value) {
    if (_selectedReportType != value) {
      _selectedReportType = value;
      notifyListeners();
    }
  }

  void _setCurrentFilter(FilterState value) {
    if (_currentFilter != value) {
      _currentFilter = value;
      notifyListeners();
    }
  }

  /// Load dashboard data with performance monitoring and error handling
  Future<void> loadDashboard() async {
    await PerformanceMonitor.measureLoadTime(
      'Dashboard Load',
      () async {
        try {
          _setLoading(true);
          _setErrorMessage('');

          // Validate filter first
          final filterValidation = ReportsErrorHandler.validateFilterState(_currentFilter);
          if (!filterValidation.isValid) {
            _setErrorMessage(filterValidation.firstError);
            _setDashboardData(ReportsErrorHandler.handleReportError(
              filterValidation.firstError,
              ReportType.dashboard,
              retryCallback: loadDashboard,
            ));
            return;
          }

          // Check cache first with error handling
          final cachedData = ReportsErrorHandler.handleCacheError(
            'Dashboard Cache Read',
            () => _cacheService.getCachedReport(ReportType.dashboard, _currentFilter),
            () => null,
          );

          if (cachedData != null) {
            _setDashboardData(cachedData);
            _setLoading(false);
            PerformanceMonitor.checkMemoryUsage('Dashboard Cache Hit');
            return;
          }

          // Load fresh data
          final data = await _reportsService.getDashboardReport(_currentFilter);

          // Validate report data
          final reportValidation = ReportsErrorHandler.validateReportData(data);
          if (!reportValidation.isValid) {
            throw Exception(reportValidation.firstError);
          }

          _setDashboardData(data);

          // Cache the result with error handling
          ReportsErrorHandler.handleCacheError(
            'Dashboard Cache Write',
            () {
              _cacheService.cacheReport(ReportType.dashboard, _currentFilter, data);
              return true;
            },
            () => false,
          );

          // Log warnings if any
          if (reportValidation.hasWarnings) {
            _setErrorMessage(reportValidation.firstWarning);
          }

        } catch (e) {
          _setErrorMessage(ReportsErrorHandler.getUserFriendlyMessage(e));
          _setDashboardData(ReportsErrorHandler.handleReportError(
            e,
            ReportType.dashboard,
            context: 'Dashboard loading failed',
            retryCallback: loadDashboard,
          ));
        } finally {
          _setLoading(false);
        }
      },
    );
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    _cacheService.invalidateReportType(ReportType.dashboard);
    await loadDashboard();
  }

  /// Select report type and load data
  Future<void> selectReportType(ReportType type) async {
    if (_selectedReportType == type) return;

    _setSelectedReportType(type);
    await loadCurrentReport();
  }

  /// Load current report data
  Future<void> loadCurrentReport() async {
    try {
      _setLoading(true);

      // Check cache first
      final cachedData = _cacheService.getCachedReport(
        _selectedReportType,
        _currentFilter,
      );

      if (cachedData != null) {
        _setCurrentReportData(cachedData);
        _setLoading(false);
        return;
      }

      // Load fresh data based on report type
      ReportData data;
      switch (_selectedReportType) {
        case ReportType.dashboard:
          data = await _reportsService.getDashboardReport(_currentFilter);
          break;
        case ReportType.cattle:
          data = await _reportsService.getCattleReport(_currentFilter);
          break;
        case ReportType.milk:
          data = await _reportsService.getMilkReport(_currentFilter);
          break;
        case ReportType.health:
          data = await _reportsService.getHealthReport(_currentFilter);
          break;
        case ReportType.breeding:
          // Placeholder - implement when breeding service is ready
          data = ReportData.empty(ReportType.breeding);
          break;
        case ReportType.weight:
          // Placeholder - implement when weight service is ready
          data = ReportData.empty(ReportType.weight);
          break;
        case ReportType.financial:
          data = await _reportsService.getFinancialReport(_currentFilter);
          break;
      }

      _setCurrentReportData(data);

      // Cache the result
      _cacheService.cacheReport(_selectedReportType, _currentFilter, data);

    } catch (e) {
      _setCurrentReportData(ReportData.empty(_selectedReportType));
      _setErrorMessage('Failed to load report: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update date range filter
  Future<void> updateDateRange(DateTime startDate, DateTime endDate) async {
    final newFilter = _currentFilter.copyWith(
      startDate: startDate,
      endDate: endDate,
    );

    _setCurrentFilter(newFilter);

    // Use selective cache invalidation for better performance
    _cacheService.invalidateSelective(ReportType.dashboard, newFilter);

    // Reload current data
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Update cattle filter
  Future<void> updateCattleFilter(List<String> cattleIds) async {
    final newFilter = _currentFilter.copyWith(cattleIds: cattleIds);
    _setCurrentFilter(newFilter);

    // Use selective cache invalidation for better performance
    _cacheService.invalidateSelective(ReportType.dashboard, newFilter);
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    _setCurrentFilter(const FilterState());

    // Clear all cache when clearing filters
    _cacheService.invalidateAll();
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Export dashboard as PDF
  Future<String?> exportDashboardPDF() async {
    final data = _dashboardData;
    if (data == null) {
      _setErrorMessage('No dashboard data to export');
      return 'No dashboard data to export';
    }

    try {
      await _sharingService.downloadPDF(data);
      return null; // Success
    } catch (e) {
      final errorMsg = 'Failed to export PDF: $e';
      _setErrorMessage(errorMsg);
      return errorMsg;
    }
  }

  /// Export dashboard as Excel
  Future<String?> exportDashboardExcel() async {
    final data = _dashboardData;
    if (data == null) {
      _setErrorMessage('No dashboard data to export');
      return 'No dashboard data to export';
    }

    try {
      await _sharingService.downloadExcel(data);
      return null; // Success
    } catch (e) {
      final errorMsg = 'Failed to export Excel: $e';
      _setErrorMessage(errorMsg);
      return errorMsg;
    }
  }

  /// Export current report
  Future<String?> exportCurrentReport(ExportFormat format, ExportType type) async {
    final data = _currentReportData ?? _dashboardData;
    if (data == null) {
      _setErrorMessage('No report data to export');
      return 'No report data to export';
    }

    try {
      final config = format == ExportFormat.pdf
          ? ExportConfig.pdf(type: type)
          : ExportConfig.excel(type: type);

      switch (type) {
        case ExportType.download:
          if (format == ExportFormat.pdf) {
            await _sharingService.downloadPDF(data, config: config);
          } else {
            await _sharingService.downloadExcel(data, config: config);
          }
          break;
        case ExportType.share:
          if (format == ExportFormat.pdf) {
            await _sharingService.sharePDF(data, config: config);
          } else {
            await _sharingService.shareExcel(data, config: config);
          }
          break;
        case ExportType.email:
          if (format == ExportFormat.pdf) {
            await _sharingService.emailPDF(data, config: config);
          } else {
            await _sharingService.emailExcel(data, config: config);
          }
          break;
        case ExportType.print:
          await _sharingService.printPDF(data, config: config);
          break;
      }
      return null; // Success
    } catch (e) {
      final errorMsg = 'Failed to export report: $e';
      _setErrorMessage(errorMsg);
      return errorMsg;
    }
  }

  /// Batch export all reports
  Future<String?> batchExportAll() async {
    try {
      _setLoading(true);

      // Generate all report types
      final reports = <ReportData>[];

      for (final type in ReportType.values) {
        try {
          ReportData data;
          switch (type) {
            case ReportType.dashboard:
              data = await _reportsService.getDashboardReport(_currentFilter);
              break;
            case ReportType.cattle:
              data = await _reportsService.getCattleReport(_currentFilter);
              break;
            case ReportType.milk:
              data = await _reportsService.getMilkReport(_currentFilter);
              break;
            case ReportType.health:
              data = await _reportsService.getHealthReport(_currentFilter);
              break;
            case ReportType.financial:
              data = await _reportsService.getFinancialReport(_currentFilter);
              break;
            default:
              data = ReportData.empty(type);
          }

          if (data.hasData) {
            reports.add(data);
          }
        } catch (e) {
          // Skip failed reports
          continue;
        }
      }

      if (reports.isEmpty) {
        _setErrorMessage('No reports available for export');
        return 'No reports available for export';
      }

      // Export each report as PDF
      for (final report in reports) {
        await _sharingService.downloadPDF(report);
      }

      return '${reports.length} reports exported successfully';

    } catch (e) {
      final errorMsg = 'Batch export failed: $e';
      _setErrorMessage(errorMsg);
      return errorMsg;
    } finally {
      _setLoading(false);
    }
  }

  /// Get cache statistics
  String getCacheStats() {
    final stats = _cacheService.getCacheStats();
    return stats.toString();
  }

  /// Clear cache
  String clearCache() {
    _cacheService.invalidateAll();
    return 'All cached reports have been cleared';
  }
}